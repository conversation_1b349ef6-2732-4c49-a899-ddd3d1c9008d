/**
 * Migration Script: Create Stripe customers for existing free users
 *
 * This script finds all free users who don't have Stripe customers
 * and creates them with $0 subscriptions so they can upgrade properly.
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { createClient } = require('@supabase/supabase-js');
const Stripe = require('stripe');

// Debug environment variables
console.log('🔍 Environment Variables Check:');
console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Found' : '❌ Missing');
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ Found' : '❌ Missing');
console.log('STRIPE_SECRET_KEY:', process.env.STRIPE_SECRET_KEY ? '✅ Found' : '❌ Missing');
console.log('NODE_ENV:', process.env.NODE_ENV || 'undefined');

// Initialize clients
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Get the correct price ID based on which Stripe keys are being used
// Check if we're using live keys by looking at the secret key prefix
const isUsingLiveKeys = process.env.STRIPE_SECRET_KEY?.startsWith('sk_live_');
const FREE_PRICE_ID = isUsingLiveKeys
  ? process.env.STRIPE_LIVE_FREE_PRICE_ID
  : process.env.STRIPE_TEST_FREE_PRICE_ID;

async function migrateFreeUsers() {
  console.log('🔄 Starting migration of existing free users to Stripe...');
  console.log(`Stripe Mode: ${isUsingLiveKeys ? 'LIVE' : 'TEST'}`);
  console.log(`Free Price ID: ${FREE_PRICE_ID}`);

  try {
    // Find all free subscriptions without Stripe customers
    const { data: freeSubscriptions, error } = await supabase
      .from('subscriptions')
      .select(`
        user_id,
        stripe_customer_id,
        tier,
        status
      `)
      .eq('tier', 'free')
      .is('stripe_customer_id', null);

    if (error) {
      console.error('❌ Error fetching free subscriptions:', error);
      return;
    }

    if (!freeSubscriptions || freeSubscriptions.length === 0) {
      console.log('✅ No free subscriptions found without Stripe customers');
      return;
    }

    console.log(`📊 Found ${freeSubscriptions.length} free subscriptions to migrate`);

    let successCount = 0;
    let errorCount = 0;

    for (const subscription of freeSubscriptions) {
      try {
        // Get user details from auth.users and user_profiles
        const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(subscription.user_id);

        if (authError || !authUser.user) {
          console.error(`❌ Error getting user email for ${subscription.user_id}:`, authError);
          errorCount++;
          continue;
        }

        // Get user profile for full name
        const { data: userProfile } = await supabase
          .from('user_profiles')
          .select('full_name')
          .eq('id', subscription.user_id)
          .single();

        const userEmail = authUser.user.email;
        const fullName = userProfile?.full_name || '';
        console.log(`\n🔄 Processing user: ${userEmail}`);

        // Create Stripe customer
        const customer = await stripe.customers.create({
          email: userEmail,
          name: fullName,
          metadata: {
            user_id: subscription.user_id,
            migrated: 'true',
            migration_date: new Date().toISOString(),
          },
        });

        console.log(`✅ Created Stripe customer: ${customer.id}`);

        // Create free subscription
        const stripeSubscription = await stripe.subscriptions.create({
          customer: customer.id,
          items: [
            {
              price: FREE_PRICE_ID,
            },
          ],
          metadata: {
            user_id: subscription.user_id,
            tier: 'free',
            migrated: 'true',
          },
        });

        console.log(`✅ Created free subscription: ${stripeSubscription.id}`);

        // Note: stripe_customer_id is stored in subscriptions table, not user_profiles
        // So we don't need to update user_profiles

        // Update subscription record with Stripe IDs
        const { error: subscriptionError } = await supabase
          .from('subscriptions')
          .update({
            stripe_customer_id: customer.id,
            stripe_subscription_id: stripeSubscription.id,
            updated_at: new Date().toISOString(),
          })
          .eq('user_id', subscription.user_id)
          .eq('tier', 'free');

        if (subscriptionError) {
          console.error(`❌ Error updating subscription record:`, subscriptionError);
          errorCount++;
          continue;
        }

        console.log(`✅ Successfully migrated user: ${userEmail}`);
        successCount++;

        // Add small delay to avoid rate limits
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (userError) {
        console.error(`❌ Error migrating user ${subscription.user_id}:`, userError);
        errorCount++;
      }
    }

    console.log('\n📊 Migration Summary:');
    console.log(`✅ Successfully migrated: ${successCount} users`);
    console.log(`❌ Failed migrations: ${errorCount} users`);
    console.log(`📈 Total processed: ${successCount + errorCount} users`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

// Run the migration
if (require.main === module) {
  migrateFreeUsers()
    .then(() => {
      console.log('\n🎉 Migration completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateFreeUsers };
